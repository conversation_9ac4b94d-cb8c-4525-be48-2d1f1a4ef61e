<?php
/**
 * Security Test Cases for CSMS File Upload Functionality
 * 
 * This script contains comprehensive test cases to verify that the security
 * fixes for file upload vulnerabilities are working correctly.
 * 
 * <AUTHOR> Agent Security Implementation
 * @version 1.0
 * @date 2025-07-10
 */

require_once 'include/secure_file_upload.php';

class FileUploadSecurityTests {
    
    private $testResults = [];
    private $secureUpload;
    
    public function __construct() {
        $this->secureUpload = new SecureFileUpload('test/');
    }
    
    /**
     * Run all security tests
     */
    public function runAllTests() {
        echo "<h1>CSMS File Upload Security Test Results</h1>\n";
        echo "<style>
            .pass { color: green; font-weight: bold; }
            .fail { color: red; font-weight: bold; }
            .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        </style>\n";
        
        $this->testFileExtensionValidation();
        $this->testMimeTypeValidation();
        $this->testFileSizeValidation();
        $this->testFileSignatureValidation();
        $this->testPathTraversalPrevention();
        $this->testMaliciousFileUpload();
        $this->testBase64Upload();
        $this->testEmptyFileHandling();
        
        $this->displaySummary();
    }
    
    /**
     * Test file extension validation
     */
    private function testFileExtensionValidation() {
        echo "<div class='test-case'>";
        echo "<h3>Test: File Extension Validation</h3>";
        
        // Test allowed extensions
        $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];
        foreach ($allowedExtensions as $ext) {
            $result = $this->testFileUpload("test.$ext", 'image/jpeg', 'valid content');
            $this->recordTest("Allowed extension: $ext", true, $result['success']);
        }
        
        // Test disallowed extensions
        $disallowedExtensions = ['php', 'exe', 'bat', 'sh', 'js', 'html'];
        foreach ($disallowedExtensions as $ext) {
            $result = $this->testFileUpload("malicious.$ext", 'text/plain', '<?php echo "hack"; ?>');
            $this->recordTest("Blocked extension: $ext", false, $result['success']);
        }
        
        echo "</div>";
    }
    
    /**
     * Test MIME type validation
     */
    private function testMimeTypeValidation() {
        echo "<div class='test-case'>";
        echo "<h3>Test: MIME Type Validation</h3>";
        
        // Test allowed MIME types
        $allowedMimes = [
            'application/pdf' => 'test.pdf',
            'image/jpeg' => 'test.jpg',
            'image/png' => 'test.png',
            'image/gif' => 'test.gif'
        ];
        
        foreach ($allowedMimes as $mime => $filename) {
            $result = $this->testFileUpload($filename, $mime, 'valid content');
            $this->recordTest("Allowed MIME: $mime", true, $result['success']);
        }
        
        // Test disallowed MIME types
        $disallowedMimes = [
            'application/x-php' => 'test.php',
            'text/html' => 'test.html',
            'application/javascript' => 'test.js'
        ];
        
        foreach ($disallowedMimes as $mime => $filename) {
            $result = $this->testFileUpload($filename, $mime, 'malicious content');
            $this->recordTest("Blocked MIME: $mime", false, $result['success']);
        }
        
        echo "</div>";
    }
    
    /**
     * Test file size validation
     */
    private function testFileSizeValidation() {
        echo "<div class='test-case'>";
        echo "<h3>Test: File Size Validation</h3>";
        
        // Test file within size limit
        $smallContent = str_repeat('a', 1024); // 1KB
        $result = $this->testFileUpload('small.pdf', 'application/pdf', $smallContent);
        $this->recordTest("Small file (1KB)", true, $result['success']);
        
        // Test file exceeding size limit
        $largeContent = str_repeat('a', 3 * 1024 * 1024); // 3MB
        $result = $this->testFileUpload('large.pdf', 'application/pdf', $largeContent);
        $this->recordTest("Large file (3MB)", false, $result['success']);
        
        // Test empty file
        $result = $this->testFileUpload('empty.pdf', 'application/pdf', '');
        $this->recordTest("Empty file", false, $result['success']);
        
        echo "</div>";
    }
    
    /**
     * Test file signature validation
     */
    private function testFileSignatureValidation() {
        echo "<div class='test-case'>";
        echo "<h3>Test: File Signature Validation</h3>";
        
        // Test valid PDF signature
        $validPdfContent = hex2bin('25504446') . 'rest of pdf content';
        $result = $this->testFileUpload('valid.pdf', 'application/pdf', $validPdfContent);
        $this->recordTest("Valid PDF signature", true, $result['success']);
        
        // Test invalid signature (PHP code with PDF extension)
        $invalidContent = '<?php echo "malicious code"; ?>';
        $result = $this->testFileUpload('fake.pdf', 'application/pdf', $invalidContent);
        $this->recordTest("Invalid PDF signature", false, $result['success']);
        
        echo "</div>";
    }
    
    /**
     * Test path traversal prevention
     */
    private function testPathTraversalPrevention() {
        echo "<div class='test-case'>";
        echo "<h3>Test: Path Traversal Prevention</h3>";
        
        $pathTraversalNames = [
            '../../../etc/passwd',
            '..\\..\\windows\\system32\\config\\sam',
            'normal/../../../etc/passwd',
            'test.pdf/../../../malicious.php'
        ];
        
        foreach ($pathTraversalNames as $filename) {
            $result = $this->testFileUpload($filename, 'application/pdf', 'content');
            $this->recordTest("Path traversal: $filename", false, $result['success']);
        }
        
        echo "</div>";
    }
    
    /**
     * Test malicious file upload attempts
     */
    private function testMaliciousFileUpload() {
        echo "<div class='test-case'>";
        echo "<h3>Test: Malicious File Upload Prevention</h3>";
        
        // Test PHP web shell
        $phpShell = '<?php system($_GET["cmd"]); ?>';
        $result = $this->testFileUpload('shell.php', 'application/x-php', $phpShell);
        $this->recordTest("PHP web shell", false, $result['success']);
        
        // Test double extension
        $result = $this->testFileUpload('image.jpg.php', 'image/jpeg', $phpShell);
        $this->recordTest("Double extension attack", false, $result['success']);
        
        // Test null byte injection
        $result = $this->testFileUpload("image.jpg\x00.php", 'image/jpeg', $phpShell);
        $this->recordTest("Null byte injection", false, $result['success']);
        
        echo "</div>";
    }
    
    /**
     * Test base64 upload functionality
     */
    private function testBase64Upload() {
        echo "<div class='test-case'>";
        echo "<h3>Test: Base64 Upload Security</h3>";
        
        // Test valid base64 upload
        $validContent = 'Valid PDF content';
        $base64Data = 'data:application/pdf;base64,' . base64_encode($validContent);
        $result = $this->secureUpload->uploadBase64File($base64Data, 'test.pdf');
        $this->recordTest("Valid base64 upload", true, $result['success']);
        
        // Test invalid base64 format
        $invalidBase64 = 'invalid base64 data';
        $result = $this->secureUpload->uploadBase64File($invalidBase64, 'test.pdf');
        $this->recordTest("Invalid base64 format", false, $result['success']);
        
        // Test malicious base64 content
        $maliciousContent = '<?php echo "hack"; ?>';
        $maliciousBase64 = 'data:application/pdf;base64,' . base64_encode($maliciousContent);
        $result = $this->secureUpload->uploadBase64File($maliciousBase64, 'malicious.pdf');
        $this->recordTest("Malicious base64 content", false, $result['success']);
        
        echo "</div>";
    }
    
    /**
     * Test empty file handling
     */
    private function testEmptyFileHandling() {
        echo "<div class='test-case'>";
        echo "<h3>Test: Empty File Handling</h3>";
        
        // Test empty filename
        $result = $this->testFileUpload('', 'application/pdf', 'content');
        $this->recordTest("Empty filename", false, $result['success']);
        
        // Test empty content
        $result = $this->testFileUpload('test.pdf', 'application/pdf', '');
        $this->recordTest("Empty content", false, $result['success']);
        
        echo "</div>";
    }
    
    /**
     * Helper method to test file upload
     */
    private function testFileUpload($filename, $mimeType, $content) {
        $tempFile = tempnam(sys_get_temp_dir(), 'test');
        file_put_contents($tempFile, $content);
        
        $file = [
            'tmp_name' => $tempFile,
            'name' => $filename,
            'size' => strlen($content),
            'type' => $mimeType,
            'error' => UPLOAD_ERR_OK
        ];
        
        $result = $this->secureUpload->uploadFile($file);
        unlink($tempFile);
        
        return $result;
    }
    
    /**
     * Record test result
     */
    private function recordTest($testName, $expected, $actual) {
        $passed = ($expected === $actual);
        $this->testResults[] = [
            'name' => $testName,
            'expected' => $expected,
            'actual' => $actual,
            'passed' => $passed
        ];
        
        $status = $passed ? "<span class='pass'>PASS</span>" : "<span class='fail'>FAIL</span>";
        echo "<p>$status - $testName</p>";
    }
    
    /**
     * Display test summary
     */
    private function displaySummary() {
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($test) {
            return $test['passed'];
        });
        $passedCount = count($passedTests);
        $failedCount = $totalTests - $passedCount;
        
        echo "<div class='test-case'>";
        echo "<h2>Test Summary</h2>";
        echo "<p><strong>Total Tests:</strong> $totalTests</p>";
        echo "<p><strong class='pass'>Passed:</strong> $passedCount</p>";
        echo "<p><strong class='fail'>Failed:</strong> $failedCount</p>";
        
        if ($failedCount > 0) {
            echo "<h3>Failed Tests:</h3>";
            foreach ($this->testResults as $test) {
                if (!$test['passed']) {
                    echo "<p class='fail'>- {$test['name']}</p>";
                }
            }
        }
        
        echo "</div>";
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'security_test_cases.php') {
    $tests = new FileUploadSecurityTests();
    $tests->runAllTests();
}
?>
