<?
ob_start();
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
require_once 'helper.php';
include ('../include/email.php');
require_once ('../security_helper.php');
sanitize_global_input();
$email = new Email();
$fungsi=new ex_fungsi();
$pdfExporter = new PDFExporter();
$conn=$fungsi->ex_koneksi();

$halaman_id=3116;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$page="form_approval_invoice.php?no_ba=".$_GET["no_ba"];

$mp_coics=$fungsi->getComin($conn,$user_org);
// if(count($mp_coics)>0){
//     unset($inorg);$orgcounter=0;
//     foreach ($mp_coics as $keyOrg => $valorgm){
//           $inorg .="'".$keyOrg."',";
//           $orgcounter++;
//     }
//     $orgIn= rtrim($inorg, ',');        
// }else{
   $orgIn= $user_org;
// }
// ceklogin 

if(isset($_POST["approve"]) && $_POST["approve"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<div class="alert alert-warning" role="alert">
			Approve invoice?
		</div>
		<form method="post" action="">
			<button type="submit" name="approve" value="approve" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Approve&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}
if(isset($_POST["reject"]) && $_POST["reject"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Reject invoice?
				<br/>
				<strong>Alasan:</strong>
				<textarea style="width: 100%; height: 100px;" name="komentar" maxlength="255" required="required"></textarea>
			</div>
			<button type="submit" name="reject" value="reject" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Reject&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}
if(isset($_POST["cancelInv"]) && $_POST["cancelInv"] == '') {
?>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
	<div class="alert alert-info" role="alert">
		<form method="post" action="">
			<div class="alert alert-warning" role="alert">
				Cancel invoice?
			</div>
			<button type="submit" name="cancelInv" value="cancelInv" style="margin-left: 16px; background-color: rgba(0,0,0,0); border: 0px;">&lt;&lt;&nbsp;&nbsp;Cancel&nbsp;&nbsp;&gt;&gt;</button>
			<a href="<?=$page?>" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
		</form>
	</div>
</div>
<?
	exit;
}


if((isset($_POST["approve"]) && $_POST["approve"] == 'approve') || (isset($_POST["reject"]) && $_POST["reject"] == 'reject') || (isset($_POST["cancelInv"]) && $_POST["cancelInv"] == 'cancelInv')) {
	$no_ba = $_GET["no_ba"];
	$sql_invoice_ba = "SELECT EX_BA_INVOICE.ID, EX_BA_INVOICE.NO_INVOICE,EX_BA_INVOICE.NO_BA,EX_BA_INVOICE.NO_FAKTUR_PAJAK,EX_BA_INVOICE.STATUS_BA_INVOICE,EX_BA_INVOICE.LAMPIRAN,EX_BA_INVOICE.DIPAKAI,EX_BA_INVOICE.KOMENTAR_REJECT, EX_BA_INVOICE.CREATED_BY,EX_BA_INVOICE.CREATED_AT, to_char(EX_BA_INVOICE.TGL_FAKTUR_PAJAK,'DD-MM-YYYY') AS TGL_FAKTUR_PAJAK1, EX_INVOICE.NO_INVOICE_SAP FROM EX_BA_INVOICE LEFT JOIN EX_INVOICE ON EX_INVOICE.NO_INVOICE = EX_BA_INVOICE.NO_INVOICE WHERE EX_BA_INVOICE.NO_BA = '$no_ba' AND EX_BA_INVOICE.DIPAKAI = 1";
	$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
	oci_execute($query_invoice_ba);

	$data_invoice_ba = array();
	while($row = oci_fetch_array($query_invoice_ba)) {
		$data_invoice_ba = $row;
	}
	
	if(isset($data_invoice_ba["ID"])) {
		if(isset($_POST["approve"])){ 
			$status_ba = 90;
			$keter = "Aprove Manager ";
			 
		}
		if(isset($_POST["reject"])){ 
			$status_ba = 80;
			$keter = "Reject Manager "; 
		}
		if(isset($_POST["cancelInv"])){
			$status_ba = 2;
			$keter = "Cancel";
		}
		// $status_ba = isset($_POST["approve"]) ? 50 : 40; 
		$action = "approval_verifikasi_invoice";
		include ('formula_prod.php');

		// Generate PPL
		// Menyimpan pdf ke dalam file di CSMS
		$adaPPL = false;
		$isi_lampiran = json_decode($data_invoice_ba["LAMPIRAN"]);
		foreach ($isi_lampiran as $key => $value) {
			if ($value[0] == 'PPL') {
				$adaPPL = true;
			}
		}
		if(!$adaPPL){
			$respPdfPPL = $pdfExporter->draftPPL($data_invoice_ba[NO_INVOICE]);
			$filenamePPL = md5(date("YmdHis") . 'ppl') . '.pdf';
			$pdf = fopen('lampiran/' . $filenamePPL, 'w');
			fwrite($pdf, $respPdfPPL);
			fclose($pdf);

			$nomor_ppl = $data_invoice_ba[NO_INVOICE];
			if(isset($data_invoice_ba[NO_INVOICE_SAP])){
				$nomor_ppl = $data_invoice_ba[NO_INVOICE_SAP];
			}

			$isi_file = array('PPL', date('Y-m-d'), 'PPL - ' . $nomor_ppl . '.pdf', $filenamePPL, $_SESSION['user_name']);
			$isi_lampiran = json_decode($data_invoice_ba[LAMPIRAN]);
			array_push($isi_lampiran, $isi_file);
			$json_isi_file = json_encode($isi_lampiran);

			$field_names = array('LAMPIRAN');
			$field_data = array($json_isi_file);
			$tablename = "EX_BA_INVOICE";
			$field_id = array('NO_BA', 'DIPAKAI');
			$value_id = array($no_ba, 1);
			$fungsi->update($conn, $field_names, $field_data, $tablename, $field_id, $value_id);
		}
		// end Generate PPL

		//sendEmail
		$sql = "SELECT ALAMAT_EMAIL FROM TB_USER_BOOKING WHERE ID = ".$data_invoice_ba[CREATED_BY];
		$query = oci_parse($conn, $sql);
		oci_execute($query);
		$row = oci_fetch_assoc($query);
		$mailTo = $row[ALAMAT_EMAIL];
		//$mailTo = '<EMAIL>';
		$mailCc = '';

		$query = "SELECT * FROM EX_BA WHERE NO_BA = '$no_ba'";
		$query = oci_parse($conn, $query);
		oci_execute($query);
		$data_ba = oci_fetch_array($query);
		$no_ba_v = $data_ba['NO_BA'];
		$org_v = $data_ba['ORG'];
		$no_vendor_v = $data_ba['NO_VENDOR'];
		$nama_vendor_v = $data_ba['NAMA_VENDOR'];
		$total_semen_v = $data_ba['KLAIM_SEMEN'];
		$total_ppdks_v = $data_ba['PPDKS'];
		$total_inv_v = $data_ba['TOTAL_INV'];

		$email_content_table = "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>
		<div align=\"center\">
		<thead>
		<tr class=\"quote\">
		<td ><strong>&nbsp;&nbsp;No.</strong></td>
		<td align=\"center\"><strong>ORG</strong></td>
		<td align=\"center\"><strong>BA REKAPITULASI</strong></td>
		<td align=\"center\"><strong>EKSPEDITUR</strong></td>
		<td align=\"center\"><strong>NAMA EKSPEDITUR</strong></td>
		<td align=\"center\"><strong>KLAIM SEMEN</strong></td>
		<td align=\"center\"><strong>PDPKS</strong></td>
		<td align=\"center\"><strong>TOTAL</strong></td>
		<td align=\"center\"><strong>STATUS</strong></td>
		</tr>
		</thead>
		<tbody>";

		$email_content_table .= " 
		<td align=\"center\">1</td>
		<td align=\"center\">".$org_v."</td>       
		<td align=\"center\">".$no_ba_v."</td>
		<td align=\"center\">".$no_vendor_v."</td>
		<td align=\"center\">".$nama_vendor_v."</td>
		<td align=\"center\">".number_format($total_semen_v,0,",",".")."</td>
		<td align=\"center\">".number_format($total_ppdks_v,0,",",".")."</td>
		<td align=\"center\">".number_format($total_inv_v,2,",",".")."</td>
		<td align=\"center\">".$keter."</td>
		</tr>";

		if($status_ba == 90){
			$email->sendMail($mailTo, $mailCc, 'Notifikasi Approve Dokumen Invoice (PPL)', $data_invoice_ba[NO_INVOICE].' / '.$data_invoice_ba["NO_INVOICE_SAP"], 'Mohon untuk ditindaklanjuti pengajuan Dokumen tsb.', $email_content_table);
		}else{
			$email->sendMail($mailTo, $mailCc, 'Notifikasi Reject Dokumen Invoice', $data_invoice_ba[NO_INVOICE].' / '.$data_invoice_ba["NO_INVOICE_SAP"], 'Mohon untuk ditindaklanjuti pengajuan Dokumen tsb.', $email_content_table);
		}
		//end sendEmail

		// header('Location: list_verifikasi_invoice.php');
		// $sql_ba_invoice = "
			// UPDATE
				// EX_BA_INVOICE
			// SET
				// STATUS_BA_INVOICE = $action,
				// UPDATED_BY = $user_id,
				// UPDATED_AT = TO_DATE('" . date("Y-m-d") . "','YYYY-MM-DD'),
				// ".($action == 40 ? "KOMENTAR_REJECT = '".$_POST['komentar']."'," : "")."
				// TGL_APPROVE_REJECT = TO_DATE('" . date("Y-m-d") . "','YYYY-MM-DD'),
				// APPROVE_REJECT_BY = $user_id
			// WHERE
				// ID = ".$data_invoice_ba["ID"]."
		// ";
		// $query_ba_invoice = oci_parse($conn, $sql_ba_invoice);
		// oci_execute($query_ba_invoice);
	}
}


$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);

$no_ba = $_GET["no_ba"];

$sql_invoice_ba = "
	SELECT
		EX_BA_INVOICE.NO_INVOICE,EX_BA_INVOICE.NO_BA,EX_BA_INVOICE.NO_FAKTUR_PAJAK,EX_BA_INVOICE.STATUS_BA_INVOICE,EX_BA_INVOICE.LAMPIRAN,EX_BA_INVOICE.DIPAKAI,EX_BA_INVOICE.KOMENTAR_REJECT, EX_BA_INVOICE.CREATED_BY,EX_BA_INVOICE.CREATED_AT,
		to_char(EX_INVOICE.TGL_INVOICE,'DD-MM-YYYY') as TGL_FAKTUR,
		EX_INVOICE.NO_KWITANSI,
		EX_INVOICE.NO_INVOICE_EX,
		EX_INVOICE.NO_REKENING,
		EX_INVOICE.BANK,
		EX_INVOICE.BVTYP,
		EX_INVOICE.BANK_CABANG
	FROM
		EX_BA_INVOICE
		LEFT JOIN EX_INVOICE ON EX_INVOICE.NO_INVOICE = EX_BA_INVOICE.NO_INVOICE
	WHERE
		EX_BA_INVOICE.NO_BA = '$no_ba'
		AND EX_BA_INVOICE.DIPAKAI = 1
		AND EX_INVOICE.ORG IN($orgIn)
";
// echo $sql_invoice_ba;
$query_invoice_ba = oci_parse($conn, $sql_invoice_ba);
oci_execute($query_invoice_ba);

$data_invoice_ba = array();
while($row = oci_fetch_array($query_invoice_ba)) {
	$data_invoice_ba = $row;
}

$sql_ba = "SELECT to_char(TGL_BA,'DD-MM-YYYY') as TGL_INVOICE1 FROM EX_BA WHERE NO_BA = '$no_ba' AND DELETE_MARK = 0";
$query_ba = oci_parse($conn, $sql_ba);
oci_execute($query_ba);

$data_ba = array();
while($row = oci_fetch_array($query_ba)) {
	$data_ba = $row;
}

// $sql = "
// 	SELECT
// 		A.*,
// 		B.CREATE_BY,
// 		B.PIC_GUDANG
// 	FROM
// 		(
// 			SELECT
// 				EX_TRANS_HDR.*,
// 				to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1,
// 				to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,
// 				to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1,
// 				to_char(TANGGAL_KIRIM,'YYYYMMDD') as TANGGAL_KIRIMF
// 			FROM
// 				EX_TRANS_HDR
// 			WHERE
// 				DELETE_MARK = '0'
// 				AND NO_BA = '$no_ba'
// 			ORDER BY
// 				PLANT,
// 				SAL_DISTRIK,
// 				KODE_KECAMATAN,
// 				KODE_PRODUK,
// 				TANGGAL_KIRIM ASC
// 		) A
// 		LEFT JOIN (
// 			SELECT
// 				m1.NO_SPJ,
// 				m1.CREATE_BY,
// 				m1.PIC_GUDANG
// 			FROM
// 				EX_INPUTCLAIM_SEMEN m1
// 				LEFT JOIN EX_INPUTCLAIM_SEMEN m2 ON (m1.NO_SPJ = m2.NO_SPJ AND m1.id < m2.id)
// 			WHERE
// 				m2.id IS NULL
// 				and m1.DELETE_MARK = '0'
// 				AND m1.STATUS = 'ELOG'
// 		) B ON (A.NO_SHP_TRN = B.NO_SPJ)
// ";
$sql= "SELECT A.ID,
    A.NO_BA,
    A.NO_VENDOR,
    A.TOTAL_INV,
    A.PAJAK_INV,
    A.NAMA_VENDOR,
    A.KLAIM_KTG,
    A.KLAIM_SEMEN,
    A.PDPKS,
    A.PDPKK,
    A.DELETE_MARK,
    A.ORG,
    A.TOTAL_INVOICE,
    A.TGL_BA,
    A.STATUS_BA,
    A.FILENAME,
    A.ALASAN_REJECT,
    A.ID_USER_APPROVAL,
    A.SIGN_STATUS_2,
    B.NO_SHP_TRN,
    B.SOLD_TO,
    B.NAMA_SOLD_TO,
    B.NO_PAJAK_EX,
    A.TGL_INVOICE,
    B.TANGGAL_DATANG,
    B.TANGGAL_BONGKAR,
    B.SHIP_TO,
    B.NAMA_SHIP_TO,
    B.ALAMAT_SHIP_TO,
    B.SAL_DISTRIK,
    B.NAMA_SAL_DIS,
    B.NO_POL,
    B.WARNA_PLAT,
    B.KODE_PRODUK,
    B.NAMA_PRODUK,
    B.KODE_KANTONG,
    B.NAMA_KANTONG,
    B.QTY_KTG_RUSAK,
    B.QTY_SEMEN_RUSAK,
    B.QTY_SHP,
    B.TOTAL_KTG_REZAK,
    B.TOTAL_SEMEN_RUSAK,
    B.TOTAL_KLAIM_KTG,
    B.NO_INVOICE ,
    B.EVIDENCE_POD1,
    B.EVIDENCE_POD2,
    B.FLAG_POD,
    B.STATUS2,
    B.GEOFENCE_POD,
    B.KETERANGAN_POD,
    B.KODE_KECAMATAN,
    B.NAMA_KECAMATAN, to_char(B.TANGGAL_INVOICE,'YYYY') as TAHUN_INVOICE1, B.SHP_COST,B.TARIF_COST FROM EX_BA A JOIN EX_TRANS_HDR B ON A.NO_BA = B.NO_BA WHERE B.NO_BA = '$no_ba' AND B.ORG IN($orgIn)  ORDER BY B.NO_SHP_TRN asc";
$query = oci_parse($conn, $sql);
oci_execute($query);
// echo $sql;

while($row = oci_fetch_array($query)) {
	
	$noe_invoice =$row[NO_INV_SAP];
	$tahunee =$row[TAHUN_INVOICE1];
	$no_ba_v[]=$row[NO_BA];
	$no_invoice_v=$row[NO_INVOICE];
	$tgl_invoice_v[]=$row[TANGGAL_INVOICE];
	$no_invoice_ex_v=$row[NO_INV_VENDOR];
	$spj_v[]=$row[NO_SHP_TRN];
	$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
	$tgl_datang_v[]=$row[TANGGAL_DATANG];
	$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR];
	$produk_v[]=$row[KODE_PRODUK];
	$nama_produk_v[]=$row[NAMA_PRODUK];
	$shp_trn_v[]=$row[NO_SHP_TRN];
	$plant_v[]=$row[PLANT]; 
	$nama_plant_v[]=$row[NAMA_PLANT]; 
	$warna_plat_v=$row[WARNA_PLAT];
	$tipe_trukd=$row[VEHICLE_TYPE];                
	$nama_vendor_v=$row[NAMA_VENDOR]; 
	$vendor_v=$row[VENDOR];
	$TANGGAL_KIRIMFgg=trim($row[TANGGAL_KIRIMF]);
	$nmmkoqq=$row[ORG];
	$lampiran1[]=$row[EVIDENCE_POD1];
	$lampiran2[]=$row[EVIDENCE_POD2];
	$tahunKIRIMFgg=substr(trim($row[TANGGAL_KIRIMF]),0,4);
	$tgl_kirim_sort[]=$row[TANGGAL_KIRIM1];
	$flag_pod[]=$row[FLAG_POD];
	$geofence_pod[]=$row[GEOFENCE_POD];
	$tarif_cost[]=doubleval($row[TARIF_COST]);
	
	//perubahan untuk costum kereta dijadikan plat hitam
	if($tipe_trukd=='205' && $vendor_v=='0000410095' ){
		$warna_plat_v='HITAM';
	}
	$sal_dis_v[]=$row[SAL_DISTRIK]; 
	$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
	$sold_to_v[]=$row[SOLD_TO];
	$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
	$ship_to_v[]=$row[SHIP_TO];
	$qty_v[]=$row[QTY_SHP];
	$um_rez=$row[UM_REZ];
	if($um_rez > 1) {
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ]/1000;
	} else {
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ];
	}
	$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
	$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
	$id_v[]=$row[ID];  
	$no_pol_v[]=$row[NO_POL];  
	$shp_cost_v[]=$row[SHP_COST];  
	$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
	$no_pajak_ex=$row[NO_PAJAK_EX];  		
	$kel=$row[KELOMPOK_TRANSAKSI];  		
	$inco=$row[INCO];  		
	$nama_kapal=$row[NAMA_KAPAL];  		
	$kode_kecamatan[]=$row[KODE_KECAMATAN];
	$tipe_truk[]=$row[VEHICLE_TYPE];
	if($row[PIC_GUDANG] != '') {
		$createby[]=$row[PIC_GUDANG];
	} else {
		if($row[CREATE_BY] != '') {
			$createby[]=$row[CREATE_BY];
		} else{
			$createby[]=$vendor;
		}
	}
	
	//Penyederhanaan Lap.OA @t 6 Feb 2012
	$item_no = trim($row[KODE_PRODUK]);                
	#Klaim Kantong
	$arr_klaim_kantong[$item_no]+=($row[TOTAL_KTG_RUSAK]+$row[TOTAL_KTG_REZAK]);  
	#Klaim Semen
	$arr_klaim_semen[$item_no]+=($row[TOTAL_SEMEN_RUSAK]+$row[PDPKS]);                
	$arr_nama_material[$item_no] = $row[NAMA_PRODUK];
	
	#Qty.
	$arr_qty_klaim_kantong[$item_no]+=$row[QTY_KTG_RUSAK];
	$arr_qty_klaim_semen[$item_no]+=$row[QTY_SEMEN_RUSAK];                
}
	$link_koneksi_sap = "../include/sapclasses/logon_data.conf"; 
	//$link_koneksi_sap = "../include/sapclasses/logon_data2.conf"; 
	$sql_kom= "SELECT KODE_KOMPONEN,NAMA_KOMPONEN,NO_GL,COST_CENTER,PROFIT_CENTER,KODE_PAJAK,PAJAK,TOTAL,SUB_TOTAL,KETERANGAN FROM EX_KOMPONEN_INV WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' order by ID asc";
	$query_kom= oci_parse($conn, $sql_kom);
               //echo $sql_kom;
		oci_execute($query_kom);
                $kode_komponen_lama=''; 
		while($row_kom=oci_fetch_array($query_kom)){ 
                    // if($row_kom[KODE_KOMPONEN]!=$kode_komponen_lama || $row_kom[KODE_KOMPONEN]=='SG-K3-2017'){ 
                        $kode_komponen_lama = $row_kom[KODE_KOMPONEN];  
                        $kode_komponen_v[]=$row_kom[KODE_KOMPONEN];  
                        $nama_komponen_v[]=$row_kom[NAMA_KOMPONEN];  
                        $no_gl_v[]=$row_kom[NO_GL];  
                        $cost_center_v[]=($row_kom[COST_CENTER] == null ? "" : $row_kom[COST_CENTER]);  
                        $profit_v[]=$row_kom[PROFIT_CENTER];  
                        $kode_pajak_v[]=$row_kom[KODE_PAJAK];  
                        $nilai_pajak_v[]=$row_kom[PAJAK];  

                        $total_komponen_v[]=$row_kom[TOTAL];  
                        $sub_total_v[]=$row_kom[SUB_TOTAL];  
                        $keterangan_komponen_v[]=$row_kom[KETERANGAN];  
                        $total_invoice = $total_invoice + $row_kom[TOTAL];
                    // }
		}
                $n_komponen = count($kode_komponen_v);
                if($n_komponen>1) $ada_klaim = false; else $ada_klaim = true;
	
	
###### alpeen inv: 0000202440  req 05-06-2014 by rofi'i no tiket 73257
$sqlU = "select last_update_date, last_updated_by, kelompok_transaksi, tipe_transaksi from ex_trans_hdr where no_invoice='$no_invoice_v' and delete_mark='0' and rownum=1";
$query_U= oci_parse($conn, $sqlU);
oci_execute($query_U);
$row_update=oci_fetch_array($query_U);
$last_update_date=$row_update[LAST_UPDATE_DATE];  
$last_updated_by=$row_update[LAST_UPDATED_BY];
$tipe_transx = $row_update[TIPE_TRANSAKSI];
$kel_trans=strtoupper(trim($row_update[KELOMPOK_TRANSAKSI]));

//liyantanto
$p1mna=0;
$sql_pjaknew="
            select to_char(TGL_PAJAK_EX,'YYYYMMDD') as TGL_PAJAK_EXF,NO_VENDOR from (
                select TGL_PAJAK_EX,NO_VENDOR from EX_INVOICE where delete_mark=0 and NO_INVOICE=
                (SELECT NO_INVOICE FROM EX_TRANS_HDR WHERE DELETE_MARK <> 1 AND KELOMPOK_TRANSAKSI = 'LAUT' AND NO_INVOICE='$no_invoice_v'
                group by NO_INVOICE)
                order by TGL_PAJAK_EX desc
            ) where rownum =1
    ";
$querycek= oci_parse($conn, $sql_pjaknew);
oci_execute($querycek);
$row_datap=oci_fetch_assoc($querycek);unset($tglfakturpajak);
$tglfakturpajak=$row_datap[TGL_PAJAK_EXF];
$VENDORpj=$row_datap[NO_VENDOR];
if($VENDORpj!='**********'){
if($tglfakturpajak!='' && $tglfakturpajak>='********'){
    $p1mna=1;
}
}

if($tipe_transx != 'BAG' && $kel_trans!='LAUT'){
   $sql_text = "SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI ";
   $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC"; 
   
   $sql= $sql_text."FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' GROUP BY ".$sql_group .""; //AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********' AND SOLD_TO != '**********'
   }else{
###### end alpeen 05-06-2014
        if($kel_trans=='LAUT'){
            $sql_text = "select ACCOUNTING_DOC,
        NO_INV_SAP,
        NO_INVOICE,
        NO_INV_VENDOR,
        VENDOR,
        SOLD_TO,
        KLAIM_KTG,
        KLAIM_REZAK,
        KLAIM_SEMEN,
        KLAIM_LEBIH,
        KLAIM_PDPKS,
        OA_SEMEN,
        WARNA_PLAT,
        KELOMPOK_TRANSAKSI from (SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,KELOMPOK_TRANSAKSI ";
            $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
        }
        else {    
            $sql_text = "select ACCOUNTING_DOC,
        NO_INV_SAP,
        NO_INVOICE,
        NO_INV_VENDOR,
        VENDOR,
        SOLD_TO,
        KLAIM_KTG,
        KLAIM_REZAK,
        KLAIM_SEMEN,
        KLAIM_LEBIH,
        KLAIM_PDPKS,
        OA_SEMEN,
        WARNA_PLAT,
        KELOMPOK_TRANSAKSI from (SELECT ACCOUNTING_DOC,NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO, SUM(TOTAL_KTG_RUSAK) AS KLAIM_KTG, SUM(TOTAL_KTG_REZAK) AS KLAIM_REZAK, SUM(TOTAL_SEMEN_RUSAK) AS KLAIM_SEMEN, SUM(KLAIM_LEBIH) AS KLAIM_LEBIH, SUM(PDPKS) AS KLAIM_PDPKS, SUM(SHP_COST) AS OA_SEMEN,WARNA_PLAT,KELOMPOK_TRANSAKSI ";
            $sql_group = "NO_INV_SAP,NO_INVOICE,NO_INV_VENDOR,VENDOR, SOLD_TO,WARNA_PLAT,KELOMPOK_TRANSAKSI,ACCOUNTING_DOC ORDER BY SOLD_TO ASC";
        }

        $sql= $sql_text."FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' GROUP BY ".$sql_group .") where KLAIM_SEMEN>=KLAIM_LEBIH";
   } 
	$query= oci_parse($conn, $sql);
	oci_execute($query);
	$total_invoice =0;
	$oa_semen_v=0;
        $arr_soldto_cek = array();
	while($row=oci_fetch_array($query)){
                $no_gl_shp_v=$row[NO_GL_SHP];
		$no_invoice_v=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$no_inv_sap_v=$row[NO_INV_SAP];
		$no_acc_doc_v=$row[ACCOUNTING_DOC];
		$warna_plat_v=$row[WARNA_PLAT]; 
		$vendor_v=$row[VENDOR]; 

		$sold_to_v[]=$row[SOLD_TO];
                $arr_soldto_cek[$row[SOLD_TO]] = $row[SOLD_TO];
                
		$klaim_ktg_v[]=$row[KLAIM_KTG];  
		$klaim_rezak_v[]=$row[KLAIM_REZAK];  
		$klaim_semen_v[]=$row[KLAIM_SEMEN];  
		$klaim_pdpks_v[]=$row[KLAIM_PDPKS];  
		$kel=$row[KELOMPOK_TRANSAKSI];  		

		$distributor = $row[SOLD_TO];
		$sql_bn= "SELECT NAMA_VENDOR,  NAMA_SOLD_TO, NO_REK_DIS, NAMA_BANK_DIS, BANK_CABANG_DIS FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice_v' AND SOLD_TO = '$distributor' ORDER BY ID DESC"; 
	$query_bn= oci_parse($conn, $sql_bn);
	oci_execute($query_bn);
	$row_bn=oci_fetch_array($query_bn);

		$nama_vendor_v=$row_bn[NAMA_VENDOR]; 
		$nama_sold_to_v[]=$row_bn[NAMA_SOLD_TO];
		$no_rek_dis_v[]=$row_bn[NO_REK_DIS];
		$nama_bank_dis_v[]=$row_bn[NAMA_BANK_DIS];
		$cabang_bank_dis_v[]=$row_bn[BANK_CABANG_DIS];  

	}
	
	  
	$link_koneksi_sap = "../include/sapclasses/logon_data.conf"; 
	  
	
	$sap = new SAPConnection();
		$sap->Connect($link_koneksi_sap);
		if ($sap->GetStatus() == SAPRFC_OK) 
			$sap->Open();
		if ($sap->GetStatus() != SAPRFC_OK) {
			 $sap->PrintStatus();
			exit;
		}

		$fce = $sap->NewFunction("BAPI_INCOMINGINVOICE_GETDETAIL");
		if ($fce == false) {
			 $sap->PrintStatus();
			exit;
		} 
		$fce->INVOICEDOCNUMBER = $noe_invoice;
		$fce->FISCALYEAR = $tahunee; 
		$fce->Call();  
		if ($fce->GetStatus() == SAPRFC_OK) { 
		
			$fce1 = $sap->NewFunction("Z_ZCMM_INVOICE_CREATE");
			if ($fce1 == false) {
				 $sap->PrintStatus();
				exit;
			} 
			$fce1->FI_SIMULATE  = "X";
			$fce1->FI_HEADER_DATA['INVOICE_IND'] = $fce->HEADERDATA['INVOICE_IND'];
			$fce1->FI_HEADER_DATA['DOC_TYPE'] = $fce->HEADERDATA['DOC_TYPE']; 
			$fce1->FI_HEADER_DATA['DOC_DATE'] = $fce->HEADERDATA['DOC_DATE'];
			$fce1->FI_HEADER_DATA['PSTNG_DATE'] =$fce->HEADERDATA['PSTNG_DATE'];
			$fce1->FI_HEADER_DATA['COMP_CODE'] = $fce->HEADERDATA['COMP_CODE'];
			$fce1->FI_HEADER_DATA['CURRENCY'] = $fce->HEADERDATA['CURRENCY'];
			$fce1->FI_HEADER_DATA['PMNTTRMS'] = $fce->HEADERDATA['PMNTTRMS'];
			$fce1->FI_HEADER_DATA['HEADER_TXT'] = $fce->HEADERDATA['HEADER_TXT'];
			$fce1->FI_HEADER_DATA['PMNT_BLOCK'] = $fce->HEADERDATA['PMNT_BLOCK'];
			$fce1->FI_HEADER_DATA['PYMT_METH'] = $fce->HEADERDATA['PYMT_METH'];
			$fce1->FI_HEADER_DATA['PARTNER_BK'] = $fce->HEADERDATA['PARTNER_BK'];
			$fce1->FI_HEADER_DATA['ITEM_TEXT'] = $fce->HEADERDATA['ITEM_TEXT'];
			
			$fce->ITEMDATA->Reset();
			while ($fce->ITEMDATA->Next()) { 
				$no_spjee = $fce->ITEMDATA->row['REF_DOC_NO'];
				$sqlS = "select  NO_ENTRY_SHEET  from EX_TRANS_HDR where NO_SHP_TRN = $no_spjee ";   
				$query_s= @oci_parse($conn, $sqlS);
				@oci_execute($query_s);
				$row_s=@oci_fetch_array($query_s); 
				$NO_ENTRY_SHEET=$row_s[NO_ENTRY_SHEET]; 
				
				$fce1->FT_ITEMDATA->row["INVOICE_DOC_ITEM"] = $fce->ITEMDATA->row['INVOICE_DOC_ITEM']; 
				$fce1->FT_ITEMDATA->row["PO_NUMBER"] = $fce->ITEMDATA->row['PO_NUMBER']; 
				$fce1->FT_ITEMDATA->row["PO_ITEM"] = $fce->ITEMDATA->row['PO_ITEM']; 
				$fce1->FT_ITEMDATA->row["REF_DOC_IT"] = $fce->ITEMDATA->row['REF_DOC_IT']; 
				$fce1->FT_ITEMDATA->row["TAX_CODE"] = $fce->ITEMDATA->row['TAX_CODE']; 
				$fce1->FT_ITEMDATA->row["ITEM_AMOUNT"] = $fce->ITEMDATA->row['ITEM_AMOUNT']; 
				$fce1->FT_ITEMDATA->row["QUANTITY"] = $fce->ITEMDATA->row['QUANTITY']; 
				$fce1->FT_ITEMDATA->row["PO_UNIT"] = $fce->ITEMDATA->row['PO_UNIT']; 
				$fce1->FT_ITEMDATA->row["PO_PR_QNT"] = $fce->ITEMDATA->row['PO_PR_QNT']; 
				$fce1->FT_ITEMDATA->row["COND_ST_NO"] = $fce->ITEMDATA->row['COND_ST_NO']; 
				$fce1->FT_ITEMDATA->row["COND_COUNT"] = $fce->ITEMDATA->row['COND_COUNT']; 
				$fce1->FT_ITEMDATA->row["SHEET_NO"] = $NO_ENTRY_SHEET; 
				$fce1->FT_ITEMDATA->row["ITEM_TEXT"] = $fce->ITEMDATA->row['ITEM_TEXT']; 
				$fce1->FT_ITEMDATA->row["SHEET_ITEM"] =  "10" ;  
				$fce1->FT_ITEMDATA->Append($fce1->FT_ITEMDATA->row);
				$total_amounte += $fce->ITEMDATA->row['ITEM_AMOUNT']; 
			} 
			
			
			$fce1->FI_HEADER_DATA['GROSS_AMOUNT'] = $total_amounte;
			 
			 
			$fce1->Call();  
			
			if ($fce1->GetStatus() == SAPRFC_OK) {  
				 $fce1-> FT_ACCIT->Reset();
				while ($fce1->FT_ACCIT->Next()) {
					$status_SIM = $fce1->FT_ACCIT->row["SHKZG"];
					if($status_SIM=="H"){
						$kredite += $fce1->FT_ACCIT->row["PSWBT"]*100;
					}else{
						$debit += $fce1->FT_ACCIT->row["PSWBT"]*100;
					} 
				}
				
				 $fce1->FT_RETURN->Reset();
				while ($fce1->FT_RETURN->Next()) {
					$status_sape = $fce1->FT_RETURN->row["TYPE"];
					$show_kete .= "<br> " . $fce1->FT_RETURN->row["TYPE"];
					$show_kete .= " " . $fce1->FT_RETURN->row["NUMBER"];
					$show_kete .= " " . $fce1->FT_RETURN->row["MESSAGE"];
				}
				 
				
			}else{   
					$fce1->PrintStatus();
					$fce1->Close();
					$sap->Close();
			}
		}else{   
				$fce->PrintStatus();
				$fce->Close();
				$sap->Close();
		}
	  
	
	

$total=count($shp_trn_v);
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Approval Invoice</th>
</tr></table>
</div>

<form method="post" action="">
	<div align="center">
		<table cellspacing="0" cellpadding="0" border="0" width="95%">
			<tbody>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Faktur Pajak</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_FAKTUR_PAJAK]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Kwitansi Expeditur</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_KWITANSI]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="width: 140px; padding: 4px; font-weight: bold;">No Invoice Expeditur</td>
					<td style="width: 16px; padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[NO_INVOICE_EX]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">Tanggal</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="padding: 4px;" colspan="3">
						<input type="text" disabled="disabled" style="width: 248px;" value="<?=$data_invoice_ba[TGL_FAKTUR]?>">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px; font-weight: bold;">No Rekening</td>
					<td style="padding: 4px; font-weight: bold;">:</td>
					<td style="width: 40px; padding: 4px;">
						<input type="text" value="<?=$data_invoice_ba[BVTYP]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td style="width: 200px; padding: 4px;">
						<input type="text" value="<?=$data_invoice_ba[BANK]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td style="width: 100px; padding: 4px;">
						<input type="text" value="<?=$data_invoice_ba[NO_REKENING]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td></td>
				</tr>
				<tr>
					<td style="padding: 4px;">&nbsp;</td>
					<td style="padding: 4px;">&nbsp;</td>
					<td style="padding: 4px;" colspan="2">
						<input type="text" value="<?=$data_invoice_ba[BANK_CABANG]?>" style="width: 100%;" disabled="disabled">
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>

		<br/>
		<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="95%" align="center">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<table align="center" class="adminlist">
							<thead>
								<tr class="quote">
									<td align="center" style="width: 40px;"><strong>NO</strong></td>
									<td align="center" style="width: 120px;"><strong>Nama Lampiran</strong></td>
									<td align="center" style="width: 100px;"><strong>Tanggal Upload</strong></td>
									<td align="center" style="width: 120px;"><strong>Lampiran</strong></td>
									<td align="center" style="width: 200px;"><strong>Submit By</strong></td> 
								</tr>
							</thead>
							<tbody id="isiLampiran"><?php
								$lampiran = $data_invoice_ba[LAMPIRAN] != "" ? json_decode($data_invoice_ba[LAMPIRAN]) : array();
								$bulan = array(null, "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC");
								foreach($lampiran as $i=>$l) {
									$tgl = explode("-", $l[1]);

									if (strtolower($l[0]) == 'ba rekapitulasi') {
										echo "
											<tr class='row-file'>
												<td align='center' class='nomor-file'>".($i+1)."</td>
												<td align='center'>".htmlspecialchars($l[0])."</td>
												<td align='center'>".$tgl[2]."-".$bulan[intval($tgl[1])]."-".$tgl[0]."</td>
												<td align='center'><a target='_blank' href=\"../ex_ba_sp/upload/".htmlspecialchars($l[3])."\">".htmlspecialchars($l[2])."</a></td>
												<td align='center'>".htmlspecialchars($l[4])."</td> 
											</tr>
										";
										continue;
									}

									echo "
										<tr class='row-file'>
											<td align='center' class='nomor-file'>".($i+1)."</td>
											<td align='center'>".htmlspecialchars($l[0])."</td>
											<td align='center'>".$tgl[2]."-".$bulan[intval($tgl[1])]."-".$tgl[0]."</td>
											<td align='center'><a target='_blank' href=\"./lampiran/".htmlspecialchars($l[3])."\">".htmlspecialchars($l[2])."</a></td>
											<td align='center'>".htmlspecialchars($l[4])."</td> 
										</tr>
									";
								}
							?></tbody>
						</table>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>
		
		<br/>
		<br/>
		
<div align="center">
<table width="95%" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data BA Rekapitulasi </span></th>
</tr>
</table>
</div> 
<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable">
		<thead>
			<tr class="quote">
				<td align="center"><strong>No.</strong></td>
				<td align="center"><strong>No BA</strong></td>
				<td align="center"><strong>Tgl BA</strong></td>
				<td align="center"><strong>Tanggal Datang</strong></td>
				<td align="center"><strong>Tanggal Bongkar</strong></td>
				<!-- <td align="center"><strong>Tgl SPJ</strong></td> -->
				<td align="center"><strong>Area LT</strong></td>
				<td align="center"><strong>No SPJ</strong></td>
				<td align="center"><strong>Nopol</strong></td>
				<td align="center"><strong>Produk</strong></td>
				<td align="center"><strong>Distributor</strong></td>
				<td align="center"><strong>K. KTG</strong></td>
				<td align="center"><strong>K. SMN</strong></td>
				<td align="center"><strong>Qty</strong></td>
				<td align="center"><strong>Tarif</strong></td>
				<td align="center"><strong>Jumlah</strong></td>
				<td align="center"><strong>POD</strong></td>
				<td align="center"><strong>Geofence</strong></td>
				<td align="center"><strong>Lampiran 1</strong></td>
				<td align="center"><strong>Lampiran 2</strong></td>
			</tr>
		</thead>
		<tbody>
			<?
				$total_qty_kantong_rusak_v = 0;
				$total_qty_semen_rusak_v = 0;
				$total_qty_v = 0;
				$total_shp_cost_v = 0;
				for($i = 0; $i < $total; $i++) {
					$total_qty_kantong_rusak_v += $qty_kantong_rusak_v[$i];
					$total_qty_semen_rusak_v += $qty_semen_rusak_v[$i];
					$total_qty_v += $qty_v[$i];
					$total_tarif_cost += $tarif_cost[$i];
					$total_shp_cost_v += $shp_cost_v[$i];
			?>
			<tr class="row<? echo ($i % 2) == 0 ? 0 : 1; ?>">
				<td align="center"><?=($i + 1)?></td>
				<td align="center"><a href="javascript:popUp('detail_ba.php?no_ba=<?=$no_ba_v[$i]?>')"><? echo $no_ba_v[$i]; ?></a></td>
				<td align="center"><?=$data_ba[TGL_INVOICE1]?></td> 
				<td align="center"><? echo $tgl_datang_v[$i]; ?></td>
				<td align="center"><? echo $tgl_bongkar_v[$i]; ?></td>
				<!-- <td align="center"><?=$tgl_kirim_sort[$i]?></td> -->
				<td align="center"><?=$sal_dis_v[$i]?></td>
				<td align="center"><?=$spj_v[$i]?></td>
				<td align="center"><?=$no_pol_v[$i]?></td>
				<td align="center"><?=trim(strtoupper($nama_produk_v[$i]),"SEMEN")?></td>
				<td align="center"><?=substr($nama_sold_to_v[$i],0,20)?></td>
				<td align="center"><?=number_format($qty_kantong_rusak_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_semen_rusak_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($qty_v[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($tarif_cost[$i], 0, ",", ".")?></td>
				<td align="center"><?=number_format($shp_cost_v[$i], 2, ",", ".")?></td>
				<td align="center"><?=$flag_pod[$i]?></td> 
				<td align="center"><a href="javascript:popUp('https://www.google.com/search?q=<?php echo $geofence_pod[$i] ?>')">GEOFENCE POD</a></td>
				<td align="center"><a href="javascript:popUp('<?php echo $lampiran1[$i] ?>')">Lampiran SPJ</a></td>
				<td align="center"><a href="javascript:popUp('<?php echo $lampiran2[$i] ?>')">Lampiran TTD SPJ</a></td>
			</tr>
			<? } ?>
		</tbody>
		<tfoot>
			<tr class="quote">
				<td align="center" colspan="10"><strong>TOTAL</strong></td>
				<td align="center"><strong><?=number_format($total_qty_kantong_rusak_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_qty_semen_rusak_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_qty_v, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_tarif_cost, 0, ",", ".")?></strong></td>
				<td align="center"><strong><?=number_format($total_shp_cost_v, 2, ",", ".")?></strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
				<td align="center"><strong>&nbsp;</strong></td>
			</tr>
		</tfoot>
	</table>
</div>
<br /><br />
<div align="center">
	<table width="95%" align="center" class="adminlist" >
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Simulate Jurnal </span></th>
	</tr>
	</table>
	<div align="center">
	<table width="95%" align="center" class="adminlist" id="myScrollTable" >
	<thead>
	  <tr class="quote">
		<td ><strong>&nbsp;&nbsp;NO.</strong></td>
		<td align="center"><strong >Kode GL </strong></td>
		 <td align="center"><strong>Kode </strong></td>
		 <td align="center"><strong>Nama Komponen </strong></td>
		 <td align="center"><strong>Cost Center </strong></td>
		 <td align="center"><strong>Profit Center </strong></td>
		 <td align="center"><strong>Pajak </strong></td>
		 <td align="center"><strong>Keterangan</strong></td>
		 <td align="center"><strong>Jumlah</strong></td> 
		 <td align="center"><strong>Mata Uang</strong></td> 
      </tr >
	  </thead>
	  <tbody> 
   
	 
<? $x = count($kode_komponen_v);
 $b = 0;
	for ($i=0; $i < count($kode_komponen_v); $i++){ 
		$b++;
		if ($kode_komponen_v[$i] == "SG0001"){
			if ($kode_pajak_v[$i] == "VN" || $kode_pajak_v[$i] == "VX") {
			$pajak_pass = "T";
			$nilai_pajak_oa = round($nilai_pajak_v[$i]);
			}
		else if ($kode_pajak_v[$i] == "YN" ) {
			$pajak_pass = "TSS";
			$nilai_pajak_oa = round($nilai_pajak_v[$i]);
			}	
			else {
			$pajak_pass = "F";
			$nilai_pajak_oa = 0;
			} 
?>		
	<tr>
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_gl_v[$i]; ?></td>
		<td align="center"><? echo $kode_komponen_v[$i]; ?></td>
		<td align="left"><? echo $nama_komponen_v[$i]; ?></td>
		<td align="center"><? echo $cost_center_v[$i]; ?></td>
		<td align="center"><? echo $profit_v[$i]; ?></td>
		<td align="center"><? echo $kd_p = $kode_pajak_v[$i]; ?></td>
		<td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
		<td align="right"><? echo number_format($tot_i = $sub_total_v[$i],0,",","."); ?></td>
		<td align="center"><? echo "IDR"; ?></td>
		</tr>
<?              $oa_nilai += $sub_total_v[$i]; //Perhitungan TOTAL
 //               echo "hehe".$sub_total_v;
                //echo '<pre>'; print_r($sub_total_v); echo '</pre>';
//WAPU zone 
if($kd_p=='WN' || $kd_p=='WX'  || $kd_p=='RX' || $kd_p=='YQ'){
    //$b++;
    number_format($tot_x = $nilai_pajak_v[$i],0,",",".");
	if ($kd_p=='YQ'){
		$tot_pjk_wn2 = $tot_x;
		$oa_nilai += $tot_pjk_wn2;
	}else {
	$b++;
	$tot_pjk_wn1 = $tot_x*-1;
    $tot_pjk_wn2 = $tot_x;  //*0.10
    $oa_nilai += $tot_pjk_wn1;
    $oa_nilai += $tot_pjk_wn2;
	}
	
    //$tot_pjk_wn1 = $tot_x*-1;
    //$tot_pjk_wn2 = $tot_x;  //*0.10
    //$oa_nilai += $tot_pjk_wn1;
    //$oa_nilai += $tot_pjk_wn2;
    
    if($kd_p=='WX' || $kd_p=='RX'){
        $kode_claim_rezak = 'SG00033';
    }else{
        $kode_claim_rezak = 'SG00027';
    }
    $mialo= "SELECT NAMA_KOMPONEN,NO_GL,KETERANGAN,TAX_CODE,KODE_KOMPONEN,PRCTR FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' AND TAX_CODE='$kd_p' AND ORG='$orginrr' AND DELETE_MARK = '0' ";
//    echo "<br>crot: ".$mialo;
    $query= oci_parse($conn, $mialo);
    oci_execute($query);
    $row=oci_fetch_array($query);
    $no_gl_wn=$row['NO_GL']; 
    $nama_komponen_wn=$row['NAMA_KOMPONEN']; 
    $keterangan_wb=$row['KETERANGAN']; 
    $pajak_wn=$row['TAX_CODE']; 
    $kdkomponen_wn=$row['KODE_KOMPONEN'];
    $profitcenter_wn=$row['PRCTR'];
 
	if($kd_p=='YQ'){
	}else 
	{
    ?>
    <tr>
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $no_gl_wn; ?></td>
        <td align="center"><? echo $kdkomponen_wn; ?></td>
        <td align="left"><? echo $nama_komponen_wn; ?></td>
        <td align="center"><? echo $cost_center_v[$i]; ?></td>
        <td align="center"><? echo $profitcenter_wn; ?></td>
        <td align="center"><? echo $pajak_wn; ?></td>
        <td align="left"><? echo $keterangan_wb; ?></td>
        <td align="right"><? echo number_format($tot_pjk_wn1,0,",","."); ?></td>
        <td align="center"><? echo "IDR"; ?></td>
    </tr>
    <?
	}
    $b++;
     if($kd_p=='WX' || $kd_p=='RX' || $kd_p=='YQ'){
        $kode_claim_rezak = 'SG00034';
    }else{
        $kode_claim_rezak = 'SG00028';
    }
    $mialo= "SELECT * FROM EX_KOMPONEN_BIAYA WHERE KODE_KOMPONEN like '$kode_claim_rezak' AND TAX_CODE='$kd_p' AND ORG='$orginrr' AND DELETE_MARK = '0' ";
//    echo "<br>dor: ".$mialo;
    $query= oci_parse($conn, $mialo);
    oci_execute($query);
    $row=oci_fetch_array($query);
    $no_gl_wn=$row['NO_GL']; 
    $nama_komponen_wn=$row['NAMA_KOMPONEN']; 
    $keterangan_wb=$row['KETERANGAN']; 
    $pajak_wn=$row['TAX_CODE']; 
    $kdkomponen_wn=$row['KODE_KOMPONEN'];
    $profitcenter_wn=$row['PRCTR'];
    ?>
    <tr>
        <td align="center"><? echo $b; ?></td>
        <td align="center"><? echo $no_gl_wn; ?></td>
        <td align="center"><? echo $kdkomponen_wn; ?></td>
        <td align="left"><? echo $nama_komponen_wn; ?></td>
        <td align="center"><? echo $cost_center_v[$i]; ?></td>
        <td align="center"><? echo $profitcenter_wn; ?></td>
        <td align="center"><? echo $pajak_wn; ?></td>
        <td align="left"><? echo $keterangan_wb; ?></td>
        <td align="right"><? echo number_format($tot_pjk_wn2,0,",","."); ?></td>
        <td align="center"><? echo "IDR"; ?></td>
    </tr>
    <?
}

            }else {
		if ($kode_komponen_v[$i] == "SG0002" or $kode_komponen_v[$i] == "SG0003" or $kode_komponen_v[$i] == "SG0004" or $kode_komponen_v[$i] == "SG0005"){ 
			// $cetak1 = "T";
			// // for($o=0; $o<$total; $o++){
            //                 if($sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********' && $sold_to_v[$o] != '**********'){
            //                     $total_kompi=0;
			// 	if ($kode_komponen_v[$i] == "SG0002" )$total_kompi = (-1)*$klaim_ktg_v[$o];
			// 	if ($kode_komponen_v[$i] == "SG0003" )$total_kompi = (-1)*$klaim_semen_v[$o];
			// 	if ($kode_komponen_v[$i] == "SG0004" )$total_kompi = (-1)*$klaim_rezak_v[$o];
			// 	if ($kode_komponen_v[$i] == "SG0005" )$total_kompi = (-1)*$klaim_pdpks_v[$o];

			// 	if ($total_kompi < 0){
            //                         if ($cetak1 == "T" ){
                                        ?>
                                        <tr>
                                        <td align="center"><? echo $b; ?></td>
                                        <td align="center"><? echo $no_gl_v[$i]; ?></td>
                                        <td align="center"><? echo $kode_komponen_v[$i]; ?></td>
                                        <td align="left"><? echo $nama_komponen_v[$i]; ?></td>
                                        <td align="center"><? echo $cost_center_v[$i]; ?></td>
                                        <td align="center"><? echo $profit_v[$i]; ?></td>
                                        <td align="center"><? echo $kode_pajak_v[$i]; ?></td>
                                        <td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
                                        <!-- <td align="right"><? echo number_format($total_kompi,0,",","."); ?></td> -->
										<td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                                        <td align="center"><? echo "IDR"; ?></td>
                                        </tr>
                                        <?
                                        // $oa_nilai+=$total_kompi; //Perhitungan TOTAL
										$oa_nilai+=$total_komponen_v[$i];
                                        // $cetak1 = "F";
                                    // }else {

                                        ?>
                                        <!-- <tr>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="left"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="center"></td>
                                        <td align="left"></td>
                                        <td align="right"><? echo number_format($total_kompi,0,",","."); ?></td> 
										<td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                                        <td align="center"><? echo "IDR"; ?></td>
                                        </tr> -->
                                        <?
                                        // $oa_nilai+=$total_kompi; //Perhitungan TOTAL
										// $oa_nilai+=$total_komponen_v[$i];
                //                     }					
				// }
                //             }
			// }
                }else{
                        ?>
                        <tr>
                        <td align="center"><? echo $b; ?></td>
                        <td align="center"><? echo $no_gl_v[$i]; ?></td>
                        <td align="center"><? echo $kode_komponen_v[$i]; ?></td>
                        <td align="left"><? echo $nama_komponen_v[$i]; ?></td>
                        <td align="center"><? echo $cost_center_v[$i]; ?></td>
                        <td align="center"><? echo $profit_v[$i]; ?></td>
                        <td align="center"><? echo $kode_pajak_v[$i]; ?></td>
                        <td align="left"><? echo $keterangan_komponen_v[$i]; ?></td>
                        <td align="right"><? echo number_format($total_komponen_v[$i],0,",","."); ?></td>
                        <td align="center"><? echo "IDR"; ?></td>
                        </tr>
                        <?	
                            $oa_nilai+=$total_komponen_v[$i]; //Perhitungan TOTAL
                }
	}
	} ?>
	<tfoot>
					<tr class="quote">
					<td colspan="6" align="center"><strong>Total</strong></td>
					<td align="center"></td>
					<td align="center"></td>
					<td align="right"><strong><? echo number_format($oa_nilai,0,",","."); ?></strong></td>
					<td align="center"></td>
					</tr>
				</tfoot>
	</table>
	
	</div> 
	<br /><br />
	<table width="50%" align="center" class="adminlist" id="myScrollTable" style="display: none;">
	<thead>
	  <tr class="quote"> 
		 <td align="center"><strong>Debet/Kredit</strong></td> 
		 <td align="center"><strong>Nilai</strong></td> 
		 <td align="center"><strong>Keterangan</strong></td> 
      </tr >
	  </thead>
	  <tbody>  	
		<tr>  
		<td align="center">Debet</td> 
		<td align="center"><? echo number_format($debit,0,",","."); ?></td> 
		<?php
			if($status_sape!="S"){
				echo $show_kete;
			} 
			$hasil_kurang = $debit - $kredite  ;
			//echo $hasil_kurang;
			if($hasil_kurang == 0 ){
				echo '<td rowspan="2"><center style="font-weight : bold;color:#3db440;font-size: 15px;">BALANCE</center></td>';
			}else{ 
				echo '<td rowspan="2"><center style="font-weight : bold;color:#f81724;font-size: 15px;">NOT BALANCE</center></td>';
			}	
		?>
		
		</tr> 
		<tr>  
		<td align="center">Kredit</td> 
		<td align="center"><? echo "- ".number_format($kredite,0,",","."); ?></td> 
		</tr> 
		
	  </tbody> 
	</table> 
<br/>
<br/>

		<table cellspacing="0" cellpadding="0" border="0" width="50%" align="center">
			<tbody>
				<tr>
					<td style="width: 500px;">
						<button type="submit" name="approve" style="margin-right: 4px; cursor: pointer; padding: 4px; background-color: #00aa00; color: #fff; border: 1px solid #000; border-radius: 4px;">Approve</button>
						<button type="submit" name="reject" style="margin-right: 4px; margin-left: 4px; cursor: pointer; text-decoration: none; padding: 4px; background-color: #ff0000; color: #fff; border: 1px solid #000; border-radius: 4px;">Reject</button>
						<button type='button' id='kembali' style="margin-left: 4px; font-size: 14px; text-decoration: none; padding: 4px; background-color: #fff; color: #000; border: 1px solid #000; border-radius: 4px;">Back</a>
					</td>
					<td></td>
				</tr>
			</tbody>
		</table>
	</div>
</form>

<form id="back-form" action="list_approval_invoice.php" method="POST">
	<input type="hidden" name="cari" value="cari" />
	<input type="hidden" name="tanggal_mulai" value="<?= $_SESSION['tgl_mulai_inv'] ?>" />
	<input type="hidden" name="tanggal_selesai" value="<?= $_SESSION['tgl_end_inv'] ?>" />
	<input type="hidden" name="no_invoice" value="<?= $_SESSION['no_inv'] ?>" />
</form>

<p>&nbsp;</p>
<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>

</p>
<? include ('../include/ekor.php'); ?>
<script src="../include/jquery.min.js"></script>
<script type="text/javascript">
	//We write the table and the div to hide the content out, so older browsers won't see it
	obj=document.getElementById("tunggu_ya");
	obj.style.display = "none";
	obj_tampil=document.getElementById("halaman_tampil");
	obj_tampil.style.display = "inline";
	$("#kembali").click(function(event) {
		event.preventDefault();
		$("#back-form").submit();
	});
</script>

</body>
</html>
