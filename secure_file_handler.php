<?php
/**
 * Secure File Handler for CSMS Application
 * 
 * This script provides secure file serving functionality for uploaded files.
 * It includes authentication, access control, and security headers to prevent
 * direct access to uploaded files and potential security vulnerabilities.
 * 
 * <AUTHOR> Agent Security Implementation
 * @version 1.0
 * @date 2025-07-10
 */

session_start();
require_once 'include/ex_fungsi.php';
require_once 'include/validasi.php';
require_once 'security_helper.php';

// Sanitize global input
sanitize_global_input();

// Configuration
const SECURE_UPLOAD_DIR = 'secure_uploads/';
const ALLOWED_FILE_TYPES = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];
const MAX_FILE_SIZE = 10485760; // 10MB for serving

/**
 * Secure File Handler Class
 */
class SecureFileHandler {
    
    private $uploadDir;
    private $user_id;
    private $user_org;
    
    public function __construct() {
        $this->uploadDir = dirname(__FILE__) . '/' . SECURE_UPLOAD_DIR;
        $this->user_id = $_SESSION['user_id'] ?? null;
        $this->user_org = $_SESSION['user_org'] ?? null;
    }
    
    /**
     * Handle file serving request
     */
    public function handleRequest() {
        // Check authentication
        if (!$this->isAuthenticated()) {
            $this->sendError(401, 'Authentication required');
            return;
        }
        
        // Get and validate filename
        $filename = $_GET['file'] ?? '';
        if (!$this->validateFilename($filename)) {
            $this->sendError(400, 'Invalid filename');
            return;
        }
        
        // Check file exists and is accessible
        $filePath = $this->uploadDir . $filename;
        if (!$this->isFileAccessible($filePath)) {
            $this->sendError(404, 'File not found or access denied');
            return;
        }
        
        // Check user permissions
        if (!$this->hasFilePermission($filename)) {
            $this->sendError(403, 'Access denied');
            return;
        }
        
        // Serve file securely
        $this->serveFile($filePath, $filename);
    }
    
    /**
     * Check if user is authenticated
     */
    private function isAuthenticated() {
        return !empty($this->user_id) && !empty($this->user_org);
    }
    
    /**
     * Validate filename for security
     */
    private function validateFilename($filename) {
        // Check for empty filename
        if (empty($filename)) {
            return false;
        }
        
        // Check for path traversal attempts
        if (strpos($filename, '..') !== false || 
            strpos($filename, '/') !== false || 
            strpos($filename, '\\') !== false) {
            return false;
        }
        
        // Check filename length
        if (strlen($filename) > 255) {
            return false;
        }
        
        // Check for valid characters (alphanumeric, underscore, dash, dot)
        if (!preg_match('/^[a-zA-Z0-9._-]+$/', $filename)) {
            return false;
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        if (!in_array($extension, ALLOWED_FILE_TYPES)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if file exists and is accessible
     */
    private function isFileAccessible($filePath) {
        // Check if file exists
        if (!file_exists($filePath)) {
            return false;
        }
        
        // Check if it's actually a file (not directory)
        if (!is_file($filePath)) {
            return false;
        }
        
        // Check if file is readable
        if (!is_readable($filePath)) {
            return false;
        }
        
        // Check file size
        $fileSize = filesize($filePath);
        if ($fileSize === false || $fileSize > MAX_FILE_SIZE) {
            return false;
        }
        
        // Ensure file is within upload directory (additional security)
        $realUploadDir = realpath($this->uploadDir);
        $realFilePath = realpath($filePath);
        
        if ($realFilePath === false || strpos($realFilePath, $realUploadDir) !== 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check user permissions for file access
     * This can be extended based on business logic
     */
    private function hasFilePermission($filename) {
        // Basic permission check - can be extended
        // For now, authenticated users can access files
        // You can implement more granular permissions here
        
        // Example: Check if file belongs to user's organization
        // This would require storing file ownership information
        
        return true; // Placeholder - implement actual permission logic
    }
    
    /**
     * Serve file with security headers
     */
    private function serveFile($filePath, $filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        $mimeType = $this->getMimeType($extension);
        $fileSize = filesize($filePath);
        
        // Set security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Set content headers
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . $fileSize);
        header('Content-Disposition: inline; filename="' . $this->sanitizeFilename($filename) . '"');
        
        // Set caching headers
        header('Cache-Control: private, max-age=3600');
        header('Pragma: private');
        
        // Prevent execution of uploaded files
        if (in_array($extension, ['php', 'html', 'htm', 'js'])) {
            header('Content-Type: text/plain');
            header('Content-Disposition: attachment; filename="' . $this->sanitizeFilename($filename) . '"');
        }
        
        // Output file content
        $handle = fopen($filePath, 'rb');
        if ($handle) {
            while (!feof($handle)) {
                echo fread($handle, 8192);
                flush();
            }
            fclose($handle);
        } else {
            $this->sendError(500, 'Error reading file');
        }
    }
    
    /**
     * Get MIME type for file extension
     */
    private function getMimeType($extension) {
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    /**
     * Sanitize filename for headers
     */
    private function sanitizeFilename($filename) {
        // Remove or replace potentially dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        return substr($filename, 0, 100); // Limit length
    }
    
    /**
     * Send error response
     */
    private function sendError($code, $message) {
        http_response_code($code);
        header('Content-Type: text/plain');
        echo $message;
        exit;
    }
}

// Handle the request
try {
    $handler = new SecureFileHandler();
    $handler->handleRequest();
} catch (Exception $e) {
    error_log('Secure file handler error: ' . $e->getMessage());
    http_response_code(500);
    echo 'Internal server error';
}
?>
