<?php
$request_method = $_SERVER["REQUEST_METHOD"];

require_once("autorisasi.php");

$fautoris = new autorisasi();
global $fautoris;
unset($dataHead);

switch ($request_method) {
  case 'POST':
    $token_in = trim($_POST['token']);
    $role = $fautoris->login($token_in);
    $jmlData = count($role['dataUserAuto']);

    if ($role['status'] == true && $jmlData > 0) {

      $user_id = trim($role['dataUserAuto']['USER_ID']);
      $dirr = $_SERVER['PHP_SELF'];
      if (empty($token_in)) {
        $responseRequest = array("responseCode" => 400, "responseMessage" => "Parameter tidak lengkap" , 'fileName' => null);
        header('Content-Type: application/json');
        echo json_encode($responseRequest);
      } else {
        $getBrand = "";

        $param['BARR'] = $_POST['BARR'];
        $param['BAMSA'] = $_POST['BAMSA'];
        $param['NO_FAKTUR'] = $_POST['NO_FAKTUR'];
        $param['FILE'] = $_POST['FILE'];

        if (!$_POST['BARR'] && !$_POST['BAMSA']) {
          if (!$_POST['BAMSA']) {
            $responseRequest = array(
              'responseCode' => 500,
              'responseMessage' => "BAMSA tidak boleh kosong",
              'fileName' => null
            );
          }else if(!$_POST['BARR']){
            $responseRequest = array(
              'responseCode' => 500,
              'responseMessage' => "BARR tidak boleh kosong",
              'fileName' => null
            );
          }else {
            $responseRequest = array(
              'responseCode' => 500,
              'responseMessage' => "Nomor BA tidak boleh kosong",
              'fileName' => null
            );
          }
          header('Content-Type: application/json');
          echo json_encode($responseRequest);die;
        }else {
          if (!$_POST['NO_FAKTUR']) {
            $responseRequest = array(
              'responseCode' => 500,
              'responseMessage' => "Nomor Faktur tidak boleh kosong",
              'fileName' => null
            );
            header('Content-Type: application/json');
            echo json_encode($responseRequest);die;
          }else {
            if (!$_FILES['FILE']['name']) {
              $responseRequest = array(
                'responseCode' => 500,
                'responseMessage' => "File tidak boleh kosong",
                'fileName' => null
              );
              header('Content-Type: application/json');
              echo json_encode($responseRequest);die;
            }else {
              if ($_FILES['FILE']['type'] !== 'application/pdf') {
                $responseRequest = array(
                  'responseCode' => 500,
                  'responseMessage' => "Upload file harus PDF",
                  'fileName' => null
                );
                header('Content-Type: application/json');
                echo json_encode($responseRequest);die;
              }else{
                $get = new sv_upload_file_faktur_pajak();
                $param["FILE_NAME"] = $_FILES['FILE']['name'];
                $result = $get->sv_data($param);
                if ($result["RETURN"]["TYPE"] != "S") {
                  $responseRequest = array(
                    'responseCode' => 500,
                    'responseMessage' => $result["RETURN"]["MESSAGE"] ? $result["RETURN"]["MESSAGE"] : "BARR tidak ditemukan",
                    'fileName' => null
                  );
                  header('Content-Type: application/json');
                  echo json_encode($responseRequest);
                }else {
                  $url = "https://dev-app1.sig.id/dev/md_paperless/index.php/upload_faktur_pajak_royalty";
                  $headers = array(
                      'Content-Type: multipart/form-data',
                  );
          
                  $base64file = base64_encode(file_get_contents($_FILES["FILE"]["tmp_name"]));
                  $body = array(
                      'FILE_NAME' => $_FILES['FILE']['name'],
                      'FILE' => $base64file
                  );
                  
                  // $options = array(
                  //     'http' => array(
                  //         'header' => "Content-type: application/json\r\n",
                  //         'method' => 'POST',
                  //         'content' => json_encode($body),
                  //     ),
                  //     'ssl' => array(
                  //         'verify_peer' => false,
                  //         'verify_peer_name' => false,
                  //         'crypto_method' => STREAM_CRYPTO_METHOD_TLSv1_2_CLIENT,
                  //     )
                  // );



                  // $context = stream_context_create($options);
                  // $result = file_get_contents($url, false, $context);
                  // $response = json_decode($result);

                  $ch = curl_init();
                  curl_setopt($ch, CURLOPT_URL, $url);
                  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                  curl_setopt($ch, CURLOPT_POST, 1);
                  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                  curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                  curl_setopt($ch, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
                  curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
                  curl_setopt($ch, CURLOPT_VERBOSE, true);
                  $response = curl_exec($ch);
          
                  if (curl_errno($ch)) {
                      $message = curl_error($ch);
                  }
                  $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                  
                  // echo "<pre>";
                  // print_r($response);
                  // echo "</pre>";exit;
                  $response = json_decode($response, true);
                  if ($response['status'] == "200") {
                    $responseRequest = array(
                      'responseCode' => 200,
                      'responseMessage' => $result["RETURN"]["MESSAGE"],
                      'fileName' => $response["file_name"]
                    );
                    header('Content-Type: application/json');
                    echo json_encode($responseRequest);
                  }else {
                    $responseRequest = array(
                      'responseCode' => 500,
                      'responseMessage' => isset($message) && $message ? $message : 'Gagal Update Data',
                      'fileName' => null
                    );
                    header('Content-Type: application/json');
                    echo json_encode($responseRequest);
                  }
                }
              }   
            }
          }
        }
      }
    } else {
      $responseRequest = array(
        'responseCode' => 401,
        'responseMessage' => "Silahkan Cek Parameter Inputan Autorisasi",
        'fileName' => null
      );
      header('Content-Type: application/json');
      echo json_encode($responseRequest);
    }
    $byLog = 'sv_upload_file_faktur_pajak';
    $log_servie = $fautoris->log_service($param, $responseRequest, $byLog, $token_in);
    break;
}

class sv_upload_file_faktur_pajak
{

  private $_basePath;
  private $_sapCon;
  private $_sapCon2;
  private $_data;

  public function __construct()
  {
    require_once("../include/sapclasses/sap.php");
    $this->_sapCon = "../include/sapclasses/logon_data.conf";
    $this->_sapCon2 = "../include/sapclasses/logon_data_mdr.conf";
  }

  function cek_koneksi()
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);
    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
    } else {
      $ResponseMessage = 'Koneksi ke SAP OK';
    }
    return $ResponseMessage;
  }

  function sv_data($param)
  {
    $sap = new SAPConnection();
    $sap->Connect($this->_sapCon);

    if ($sap->GetStatus() != 'SAPRFC_OK') {
      $ResponseMessage = 'Gagal koneksi ke SAP';
      $responseRequest = $param;
    } else {
      $sap->Open();

      if ($param['BAMSA'])
        $fce = $sap->NewFunction("ZDIS_INT_MON_BASTMSA");
      else
        $fce = $sap->NewFunction("ZDIS_INT_MON_BASTB");
      if ($fce == false) {
        $ResponseMessage = 'RFC Tidak Ditemukan RFC';
        $responseRequest = $param;
      } else {

        $data = $this->rfc($fce, $param);
        
        $fce->Close();
        $sap->Close();
        return $data;
      }
    }
  }

  function rfc($fce, $param)
  {
    if ($param['BAMSA'])
      $fce->BAMSA = $param['BAMSA'];
    else
      $fce->BASTB = $param['BARR'];

    $fce->I_TRANSACTION_TYPE = "I";
    $fce->I_FAKTUR = "X";
    $fce->NO_FAKTUR = $param['NO_FAKTUR'];
    $fce->FILE_NAME = $param['FILE_NAME'];
    $fce->FTYPE = "FP";
    
    $fce->Call();
    if ($fce->GetStatus() == SAPRFC_OK) {
      $fce->T_DATA->Reset();
      $this->_data["RETURN"] = $fce->RETURN;
    }
    return $this->_data;
  }
}
