<?php
/**
 * Secure File Upload Handler for CSMS Application
 * 
 * This class provides secure file upload functionality with comprehensive
 * validation, sanitization, and security measures to prevent common
 * file upload vulnerabilities.
 * 
 * <AUTHOR> Agent Security Implementation
 * @version 1.0
 * @date 2025-07-10
 */

class SecureFileUpload {
    
    // Configuration constants
    const MAX_FILE_SIZE = 2097152; // 2MB in bytes
    const UPLOAD_DIR_BASE = '../secure_uploads/'; // Outside web root
    const ALLOWED_EXTENSIONS = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];
    const ALLOWED_MIME_TYPES = [
        'application/pdf',
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif'
    ];
    
    // File signature validation (magic numbers)
    const FILE_SIGNATURES = [
        'pdf' => ['25504446'], // %PDF
        'jpg' => ['FFD8FF'],   // JPEG
        'jpeg' => ['FFD8FF'],  // JPEG
        'png' => ['89504E47'], // PNG
        'gif' => ['474946']    // GIF
    ];
    
    private $errors = [];
    private $uploadDir;
    
    public function __construct($subDir = '') {
        $this->uploadDir = self::UPLOAD_DIR_BASE . $subDir;
        $this->createUploadDirectory();
    }
    
    /**
     * Upload and validate a file securely
     * 
     * @param array $file $_FILES array element
     * @param string $allowedTypes Optional override for allowed types
     * @return array Result with success status and file info
     */
    public function uploadFile($file, $allowedTypes = null) {
        $this->errors = [];
        
        // Basic validation
        if (!$this->validateBasicUpload($file)) {
            return $this->getErrorResponse();
        }
        
        // File size validation
        if (!$this->validateFileSize($file['size'])) {
            return $this->getErrorResponse();
        }
        
        // File type validation
        $allowedTypes = $allowedTypes ?: self::ALLOWED_EXTENSIONS;
        if (!$this->validateFileType($file, $allowedTypes)) {
            return $this->getErrorResponse();
        }
        
        // MIME type validation
        if (!$this->validateMimeType($file['type'])) {
            return $this->getErrorResponse();
        }
        
        // File signature validation
        if (!$this->validateFileSignature($file['tmp_name'], $file['name'])) {
            return $this->getErrorResponse();
        }
        
        // Sanitize filename and generate secure name
        $secureFilename = $this->generateSecureFilename($file['name']);
        $targetPath = $this->uploadDir . '/' . $secureFilename;
        
        // Move file to secure location
        if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
            $this->errors[] = 'Failed to move uploaded file to secure location';
            return $this->getErrorResponse();
        }
        
        // Set secure file permissions
        chmod($targetPath, 0644);
        
        return [
            'success' => true,
            'filename' => $secureFilename,
            'original_name' => $file['name'],
            'size' => $file['size'],
            'type' => $file['type'],
            'path' => $targetPath,
            'url' => $this->getSecureFileUrl($secureFilename)
        ];
    }
    
    /**
     * Upload base64 encoded file securely
     * 
     * @param string $base64Data Base64 encoded file data
     * @param string $originalName Original filename
     * @param string $allowedTypes Optional override for allowed types
     * @return array Result with success status and file info
     */
    public function uploadBase64File($base64Data, $originalName, $allowedTypes = null) {
        $this->errors = [];
        
        // Validate base64 data
        if (!$this->validateBase64Data($base64Data)) {
            return $this->getErrorResponse();
        }
        
        // Extract and decode data
        list($type, $data) = explode(';', $base64Data);
        list(, $data) = explode(',', $data);
        $decodedData = base64_decode($data);
        
        if ($decodedData === false) {
            $this->errors[] = 'Invalid base64 data';
            return $this->getErrorResponse();
        }
        
        // Validate file size
        if (!$this->validateFileSize(strlen($decodedData))) {
            return $this->getErrorResponse();
        }
        
        // Extract MIME type from data URI
        $mimeType = str_replace('data:', '', $type);
        if (!$this->validateMimeType($mimeType)) {
            return $this->getErrorResponse();
        }
        
        // Validate file extension
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $allowedTypes = $allowedTypes ?: self::ALLOWED_EXTENSIONS;
        if (!in_array($extension, $allowedTypes)) {
            $this->errors[] = 'File extension not allowed: ' . $extension;
            return $this->getErrorResponse();
        }
        
        // Generate secure filename
        $secureFilename = $this->generateSecureFilename($originalName);
        $targetPath = $this->uploadDir . '/' . $secureFilename;
        
        // Write file to secure location
        if (file_put_contents($targetPath, $decodedData) === false) {
            $this->errors[] = 'Failed to save base64 file';
            return $this->getErrorResponse();
        }
        
        // Validate file signature after writing
        if (!$this->validateFileSignature($targetPath, $originalName)) {
            unlink($targetPath); // Remove invalid file
            return $this->getErrorResponse();
        }
        
        // Set secure file permissions
        chmod($targetPath, 0644);
        
        return [
            'success' => true,
            'filename' => $secureFilename,
            'original_name' => $originalName,
            'size' => strlen($decodedData),
            'type' => $mimeType,
            'path' => $targetPath,
            'url' => $this->getSecureFileUrl($secureFilename)
        ];
    }
    
    /**
     * Validate basic file upload
     */
    private function validateBasicUpload($file) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $this->errors[] = 'No valid file uploaded';
            return false;
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->errors[] = 'Upload error: ' . $this->getUploadErrorMessage($file['error']);
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate file size
     */
    private function validateFileSize($size) {
        if ($size > self::MAX_FILE_SIZE) {
            $this->errors[] = 'File size exceeds maximum allowed size of ' . 
                             number_format(self::MAX_FILE_SIZE / 1024 / 1024, 1) . 'MB';
            return false;
        }
        
        if ($size <= 0) {
            $this->errors[] = 'File is empty';
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate file type by extension
     */
    private function validateFileType($file, $allowedTypes) {
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedTypes)) {
            $this->errors[] = 'File type not allowed. Allowed types: ' . implode(', ', $allowedTypes);
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate MIME type
     */
    private function validateMimeType($mimeType) {
        if (!in_array($mimeType, self::ALLOWED_MIME_TYPES)) {
            $this->errors[] = 'MIME type not allowed: ' . $mimeType;
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate file signature (magic numbers)
     */
    private function validateFileSignature($filePath, $originalName) {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        
        if (!isset(self::FILE_SIGNATURES[$extension])) {
            $this->errors[] = 'No signature validation available for file type: ' . $extension;
            return false;
        }
        
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            $this->errors[] = 'Cannot read file for signature validation';
            return false;
        }
        
        $bytes = fread($handle, 8);
        fclose($handle);
        
        $hex = strtoupper(bin2hex($bytes));
        $validSignatures = self::FILE_SIGNATURES[$extension];
        
        foreach ($validSignatures as $signature) {
            if (strpos($hex, $signature) === 0) {
                return true;
            }
        }
        
        $this->errors[] = 'File signature does not match expected type for: ' . $extension;
        return false;
    }
    
    /**
     * Validate base64 data format
     */
    private function validateBase64Data($data) {
        if (!preg_match('/^data:([a-zA-Z0-9][a-zA-Z0-9\/+]*);base64,/', $data)) {
            $this->errors[] = 'Invalid base64 data format';
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate secure filename
     */
    private function generateSecureFilename($originalName) {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Sanitize basename
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = substr($basename, 0, 50); // Limit length
        
        // Generate unique hash
        $hash = md5(uniqid(mt_rand(), true) . $originalName . microtime());
        
        return $basename . '_' . $hash . '.' . $extension;
    }
    
    /**
     * Create upload directory if it doesn't exist
     */
    private function createUploadDirectory() {
        if (!is_dir($this->uploadDir)) {
            if (!mkdir($this->uploadDir, 0755, true)) {
                throw new Exception('Cannot create upload directory: ' . $this->uploadDir);
            }
            
            // Create .htaccess to prevent direct access
            $htaccessContent = "Order Deny,Allow\nDeny from all\n";
            file_put_contents($this->uploadDir . '/.htaccess', $htaccessContent);
        }
    }
    
    /**
     * Get secure file URL for serving files
     */
    private function getSecureFileUrl($filename) {
        return 'secure_file_handler.php?file=' . urlencode($filename);
    }
    
    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($errorCode) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        return isset($errors[$errorCode]) ? $errors[$errorCode] : 'Unknown upload error';
    }
    
    /**
     * Get error response
     */
    private function getErrorResponse() {
        return [
            'success' => false,
            'errors' => $this->errors
        ];
    }
    
    /**
     * Get all errors
     */
    public function getErrors() {
        return $this->errors;
    }
}
